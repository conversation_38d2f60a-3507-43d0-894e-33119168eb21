# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Architecture

This is a C-based SITP (Secure Industrial Transport Protocol) bridge system that provides TCP-to-SITP bridging and proxying functionality for industrial control systems.

### Core Components

The system consists of two main applications:

- **TCP Bridge** (`bin/tcp_bridge`): Acts as a TCP server that forwards data to SITP clients
  - Listens on TCP port (default 8080) and forwards to SITP protocol
  - Uses named pipes for internal component communication
  - Configurable via `bridge.conf`

- **TCP Proxy** (`bin/tcp_proxy`): Acts as a SITP-to-TCP proxy client
  - Receives data from SITP and forwards to TCP servers (e.g., NGINX)
  - Manages connection pools for efficient TCP handling
  - Configurable via `proxy.conf`

- **SITP Library** (`lib/sitp/sitp_lib.h`): Core SITP protocol implementation
  - Board-to-board communication with ID-based addressing (FF10, FF12, FF18)
  - Uses libevent for asynchronous I/O and automatic reconnection
  - Named pipe IPC with configurable protocol numbers

- **Logging System** (`lib/log/`): Colored logging framework supporting multiple levels
- **Configuration Parser** (`lib/ini/`): INI file parser for application configuration
- **Utility Libraries** (`lib/ut/`): Header-only data structures (hash tables, lists, etc.)

### Project Structure

```
src/
├── bridge/     - TCP-to-SITP bridge service implementation
├── proxy/      - SITP-to-TCP proxy service implementation  
├── common/     - Shared utilities (protocol handling, hex utils)
└── demo/       - Demo applications
```

## Build System

Uses a comprehensive Makefile with the following commands:

- `make` or `make all` - Build both bridge and proxy services
- `make bridge` - Build TCP bridge service only
- `make proxy` - Build TCP proxy service only
- `make clean` - Clean all build artifacts
- `make debug` - Build with debug symbols and logging
- `make release` - Build optimized release version
- `make install` - Install binaries to `/usr/local/bin`
- `make deps` - Show required dependencies
- `make test-colors` - Test colored log output
- `make help` - Show available make targets
- `make demo` - Build demo application
- `make uninstall` - Remove installed binaries

### Dependencies
- `libevent-dev` - Event-driven I/O library
- `build-essential` - GCC compiler and make tools

Install dependencies:
```bash
# Ubuntu/Debian
sudo apt-get install libevent-dev build-essential

# CentOS/RHEL  
sudo yum install libevent-devel gcc make
```

## SITP Library Usage

The SITP library provides a simple API for inter-process communication:

### Basic Usage Pattern

```c
#include "lib/sitp/sitp_lib.h"

// 1. Define a callback function for receiving data
void recv_callback(void *arg, uint8_t *buffer, size_t len) {
    // Process received data
    printf("Received %zu bytes\n", len);
}

// 2. Add a SITP interface
void *sitp_handle = sitp_lib_add(
    "eth0",           // Network interface (unused in current implementation)
    1500,             // MTU size
    0x8888,           // Protocol number
    0xFF10,           // Local board ID (M300)
    0xFF12,           // Remote board ID (key manager M300)
    0,                // Local port (0 = random)
    0,                // Remote port (0 = random)
    recv_callback,    // Data reception callback
    NULL              // User argument for callback
);

// 3. Start the SITP event loop (in a separate thread)
pthread_t sitp_thread;
pthread_create(&sitp_thread, NULL, sitp_thread_func, NULL);

// 4. Send data
char *message = "Hello World";
int result = sitp_lib_send(sitp_handle, (uint8_t*)message, strlen(message));
if (result == 0) {
    printf("Successfully sent %zu bytes\n", strlen(message));
}
```

### Thread Function Example

```c
void *sitp_thread_func(void *arg) {
    sitp_lib_start();  // Runs event loop until stopped
    return NULL;
}
```

### Key Features

- **Automatic Reconnection**: The library automatically handles connection drops and reconnects silently
- **Non-blocking Operations**: Send operations return immediately, simulating success when no receiver is available
- **Signal Safety**: SIGPIPE signals are handled to prevent program termination
- **Event-driven**: Uses libevent for efficient I/O handling

## Configuration

The system uses INI-format configuration files:

### Bridge Configuration (`bridge.conf`)
```ini
[tcp]
listen_ip = 127.0.0.1
listen_port = 8080

[sitp]
interface = eth0
mtu = 1500
protocol = 0x8888
local_board_id = 0xFF10
remote_board_id = 0xFF18
local_port = 0
remote_port = 0
send_delay_ns = 400

[log]
# Levels: TRACE, DEBUG, INFO, WARN, ERROR, FATAL
level = INFO
to_file = 0
filename = bridge.log

[general]
padding_size = 16
verbose = 0
```

### Proxy Configuration (`proxy.conf`) 
```ini
[sitp]
interface = eth0
mtu = 1500
protocol = 0x8888
local_board_id = 0xFF18
remote_board_id = 0xFF10
local_port = 0
remote_port = 0
send_delay_ns = 400

[target]
host = 127.0.0.1
port = 80

[connection]
timeout = 30
pool_size = 100

[log]
# Levels: TRACE, DEBUG, INFO, WARN, ERROR, FATAL
level = INFO
to_file = 0
filename = proxy.log

[general]
padding_size = 16
verbose = 0
```

## Running the Services

Start services with configuration files:
```bash
./bin/tcp_bridge -c bridge.conf
./bin/tcp_proxy -c proxy.conf
```

Both services support:
- `-c <config_file>` - Specify configuration file
- Graceful shutdown via SIGTERM/SIGINT
- Colored log output for debugging
- Configurable log levels (TRACE, DEBUG, INFO, WARN, ERROR, FATAL)
- Verbose hex dump mode for data debugging

## Demo Application

Build and run the demo application:
```bash
make demo
./bin/demo
```

## Architecture Overview

Data flow: `TCP Client → Bridge → SITP → Proxy → TCP Server (NGINX)`

1. Bridge listens for TCP connections and forwards data via SITP
2. Proxy receives SITP data and forwards to target TCP servers
3. Named pipes handle internal component communication
4. libevent provides async I/O for all network operations