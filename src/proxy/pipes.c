#include "proxy/pipes.h"
#include "log.h"
#include <errno.h>
#include <fcntl.h>
#include <string.h>
#include <unistd.h>

// Forward declarations for callbacks implemented in data_flow.c
extern void sitp_to_tcp_pipe_callback(int fd, short event, void *arg);
extern void tcp_to_sitp_pipe_callback(int fd, short event, void *arg);

static int create_pipes(proxy_pipes_t *pipes) {
  // Create SITP to TCP pipe
  if (pipe(pipes->sitp_to_tcp_pipe) == -1) {
    log_error("Failed to create sitp_to_tcp pipe: %s", strerror(errno));
    return -1;
  }

  // Create TCP to SITP pipe
  if (pipe(pipes->tcp_to_sitp_pipe) == -1) {
    log_error("Failed to create tcp_to_sitp pipe: %s", strerror(errno));
    close(pipes->sitp_to_tcp_pipe[0]);
    close(pipes->sitp_to_tcp_pipe[1]);
    return -1;
  }

  return 0;
}

static void set_pipes_nonblocking(proxy_pipes_t *pipes) {
  fcntl(pipes->sitp_to_tcp_pipe[0], F_SETFL, O_NONBLOCK);
  fcntl(pipes->sitp_to_tcp_pipe[1], F_SETFL, O_NONBLOCK);
  fcntl(pipes->tcp_to_sitp_pipe[0], F_SETFL, O_NONBLOCK);
  fcntl(pipes->tcp_to_sitp_pipe[1], F_SETFL, O_NONBLOCK);
}

static int create_events(proxy_pipes_t *pipes, struct event_base *base) {
  // Create SITP to TCP event
  pipes->sitp_to_tcp_event =
      event_new(base, pipes->sitp_to_tcp_pipe[0], EV_READ | EV_PERSIST,
                sitp_to_tcp_pipe_callback, pipes);
  if (!pipes->sitp_to_tcp_event) {
    log_error("Failed to create sitp_to_tcp event");
    return -1;
  }

  // Create TCP to SITP event
  pipes->tcp_to_sitp_event =
      event_new(base, pipes->tcp_to_sitp_pipe[0], EV_READ | EV_PERSIST,
                tcp_to_sitp_pipe_callback, pipes);
  if (!pipes->tcp_to_sitp_event) {
    log_error("Failed to create tcp_to_sitp event");
    event_free(pipes->sitp_to_tcp_event);
    pipes->sitp_to_tcp_event = NULL;
    return -1;
  }

  return 0;
}

static int add_events_to_loop(proxy_pipes_t *pipes) {
  if (event_add(pipes->sitp_to_tcp_event, NULL) < 0) {
    log_error("Failed to add sitp_to_tcp event");
    return -1;
  }

  if (event_add(pipes->tcp_to_sitp_event, NULL) < 0) {
    log_error("Failed to add tcp_to_sitp event");
    return -1;
  }

  return 0;
}

static void cleanup_pipes(proxy_pipes_t *pipes) {
  close(pipes->sitp_to_tcp_pipe[0]);
  close(pipes->sitp_to_tcp_pipe[1]);
  close(pipes->tcp_to_sitp_pipe[0]);
  close(pipes->tcp_to_sitp_pipe[1]);
}

static void cleanup_events(proxy_pipes_t *pipes) {
  if (pipes->sitp_to_tcp_event) {
    event_free(pipes->sitp_to_tcp_event);
    pipes->sitp_to_tcp_event = NULL;
  }
  if (pipes->tcp_to_sitp_event) {
    event_free(pipes->tcp_to_sitp_event);
    pipes->tcp_to_sitp_event = NULL;
  }
}

int proxy_pipes_init(proxy_pipes_t *pipes, struct event_base *base) {
  // Initialize to NULL for safe cleanup
  pipes->sitp_to_tcp_event = NULL;
  pipes->tcp_to_sitp_event = NULL;

  // Create pipes
  if (create_pipes(pipes) < 0) {
    return -1;
  }

  // Set non-blocking mode
  set_pipes_nonblocking(pipes);

  // Create events
  if (create_events(pipes, base) < 0) {
    cleanup_pipes(pipes);
    return -1;
  }

  // Add events to event loop
  if (add_events_to_loop(pipes) < 0) {
    cleanup_events(pipes);
    cleanup_pipes(pipes);
    return -1;
  }

  log_info("Proxy pipes initialized successfully");
  return 0;
}

void proxy_pipes_cleanup(proxy_pipes_t *pipes) {
  if (pipes->sitp_to_tcp_event) {
    event_free(pipes->sitp_to_tcp_event);
    pipes->sitp_to_tcp_event = NULL;
  }

  if (pipes->tcp_to_sitp_event) {
    event_free(pipes->tcp_to_sitp_event);
    pipes->tcp_to_sitp_event = NULL;
  }

  if (pipes->sitp_to_tcp_pipe[0] >= 0) {
    close(pipes->sitp_to_tcp_pipe[0]);
    close(pipes->sitp_to_tcp_pipe[1]);
    pipes->sitp_to_tcp_pipe[0] = pipes->sitp_to_tcp_pipe[1] = -1;
  }

  if (pipes->tcp_to_sitp_pipe[0] >= 0) {
    close(pipes->tcp_to_sitp_pipe[0]);
    close(pipes->tcp_to_sitp_pipe[1]);
    pipes->tcp_to_sitp_pipe[0] = pipes->tcp_to_sitp_pipe[1] = -1;
  }
}

int proxy_pipes_write_sitp_to_tcp(proxy_pipes_t *pipes, const uint8_t *data,
                                  size_t len) {
  ssize_t written = write(pipes->sitp_to_tcp_pipe[1], data, len);
  if (written < 0 && errno != EAGAIN && errno != EWOULDBLOCK) {
    log_error("Failed to write to sitp_to_tcp pipe: %s", strerror(errno));
    return -1;
  }
  return written;
}

int proxy_pipes_write_tcp_to_sitp(proxy_pipes_t *pipes, const uint8_t *data,
                                  size_t len) {
  ssize_t written = write(pipes->tcp_to_sitp_pipe[1], data, len);
  if (written < 0 && errno != EAGAIN && errno != EWOULDBLOCK) {
    log_error("Failed to write to tcp_to_sitp pipe: %s", strerror(errno));
    return -1;
  }
  return written;
}
