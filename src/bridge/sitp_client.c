#include "bridge/sitp_client.h"
#include "common/protocol.h"
#include "log.h"
#include "sitp_lib.h"
#include <stdlib.h>
#include <string.h>
#include <unistd.h>

static void sitp_recv_callback(void *arg, uint8_t *buffer, size_t len);
static void *sitp_thread_func(void *arg);

int sitp_client_init(sitp_client_t *client, bridge_config_t *config,
                     bridge_pipes_t *pipes) {
  memset(client, 0, sizeof(sitp_client_t));
  client->config = config;
  client->pipes = pipes;
  client->running = 0;

  client->sitp_handle =
      sitp_lib_add(config->sitp_interface, config->sitp_mtu,
                   config->sitp_protocol, config->sitp_local_board_id,
                   config->sitp_remote_board_id, config->sitp_local_port,
                   config->sitp_remote_port, sitp_recv_callback, client);

  if (!client->sitp_handle) {
    log_error("Failed to add SITP interface");
    return -1;
  }

  log_info("SITP client initialized:");
  log_info("  Interface    : %s", config->sitp_interface);
  log_info("  Protocol     : 0x%04x", config->sitp_protocol);
  log_info("  Local ID     : 0x%04x", config->sitp_local_board_id);
  log_info("  Remote ID    : 0x%04x", config->sitp_remote_board_id);
  log_info("  Local Port   : %d", config->sitp_local_port);
  log_info("  Remote Port  : %d", config->sitp_remote_port);
  log_info("  MTU Size     : %d", config->sitp_mtu);

  return 0;
}

int sitp_client_start(sitp_client_t *client) {
  client->running = 1;

  if (pthread_create(&client->sitp_thread, NULL, sitp_thread_func, client) !=
      0) {
    log_error("Failed to create SITP thread");
    client->running = 0;
    return -1;
  }

  log_info("SITP client started");
  return 0;
}

void sitp_client_stop(sitp_client_t *client) {
  if (client->running) {
    client->running = 0;

    if (pthread_join(client->sitp_thread, NULL) != 0) {
      log_warn("Failed to join SITP thread");
    }

    log_info("SITP client stopped");
  }
}

void sitp_client_cleanup(sitp_client_t *client) {
  sitp_client_stop(client);

  if (client->sitp_handle) {
    client->sitp_handle = NULL;
  }

  log_info("SITP client cleaned up");
}

static void sitp_recv_callback(void *arg, uint8_t *buffer, size_t len) {
  sitp_client_t *client = (sitp_client_t *)arg;

  if (!client->running) {
    return;
  }

  if (!validate_padded_message(buffer, len, client->config->padding_size)) {
    log_warn("Received invalid padded message from SITP");
    return;
  }

  ssize_t written = write(client->pipes->sitp_to_tcp_fd[1], buffer, len);
  if (written != (ssize_t)len) {
    log_warn("Failed to write SITP data to pipe: written=%zd, expected=%zu",
             written, len);
  } else {
    log_debug("Forwarded %zu bytes from SITP to pipe", len);
  }
}

static void *sitp_thread_func(void *arg) {
  sitp_client_t *client = (sitp_client_t *)arg;

  log_info("SITP thread started");
  sitp_lib_start();
  log_info("SITP thread exiting");

  return NULL;
}
