#ifndef COMMON_PROTOCOL_H
#define COMMON_PROTOCOL_H

#include <stdint.h>
#include <sys/types.h>

typedef enum {
  CMD_DATA = 0,
  CMD_DISCONNECT = 1,
  CMD_PING = 2,
  CMD_PONG = 3
} message_cmd_t;

typedef struct {
  uint32_t client_fd;
  message_cmd_t cmd;
  uint32_t data_len;
  uint8_t data[];
} message_t;

typedef struct {
  uint8_t *padding_start;
  message_t *msg;
  uint8_t *padding_end;
  uint8_t *buffer;
  size_t total_size;
} padded_message_t;

padded_message_t *create_padded_message(uint32_t client_fd, message_cmd_t cmd,
                                        const uint8_t *data, uint32_t data_len,
                                        int padding_size);

message_t *extract_message_from_padded(const uint8_t *buffer, size_t buffer_len,
                                       int padding_size);

void free_padded_message(padded_message_t *pmsg);

size_t get_padded_message_size(uint32_t data_len, int padding_size);

int validate_padded_message(const uint8_t *buffer, size_t buffer_len,
                            int padding_size);

#endif
