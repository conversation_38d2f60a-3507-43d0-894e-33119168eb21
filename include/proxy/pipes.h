#ifndef PROXY_PIPES_H
#define PROXY_PIPES_H

#include <event2/event.h>

typedef struct {
  int sitp_to_tcp_pipe[2]; // From SITP callback to TCP client
  int tcp_to_sitp_pipe[2]; // From TCP client to SITP send
  struct event *sitp_to_tcp_event;
  struct event *tcp_to_sitp_event;
} proxy_pipes_t;

int proxy_pipes_init(proxy_pipes_t *pipes, struct event_base *base);
void proxy_pipes_cleanup(proxy_pipes_t *pipes);

int proxy_pipes_write_sitp_to_tcp(proxy_pipes_t *pipes, const uint8_t *data,
                                  size_t len);
int proxy_pipes_write_tcp_to_sitp(proxy_pipes_t *pipes, const uint8_t *data,
                                  size_t len);

#endif
