#ifndef PROXY_SITP_CLIENT_H
#define PROXY_SITP_CLIENT_H

#include "proxy/config.h"
#include "proxy/pipes.h"
#include <pthread.h>
#include <stdint.h>

typedef struct {
  proxy_config_t *config;
  proxy_pipes_t *pipes;
  void *sitp_handle;
  pthread_t sitp_thread;
  int running;
} proxy_sitp_client_t;

int proxy_sitp_client_init(proxy_sitp_client_t *client, proxy_config_t *config,
                           proxy_pipes_t *pipes);
int proxy_sitp_client_start(proxy_sitp_client_t *client);
void proxy_sitp_client_stop(proxy_sitp_client_t *client);
void proxy_sitp_client_cleanup(proxy_sitp_client_t *client);

int proxy_sitp_client_send(proxy_sitp_client_t *client, const uint8_t *data,
                           size_t len);

#endif
