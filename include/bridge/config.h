#ifndef BRIDGE_CONFIG_H
#define BRIDGE_CONFIG_H

#include <stdint.h>

typedef struct {
  char *listen_ip;
  int listen_port;

  char *sitp_interface;
  int sitp_mtu;
  uint16_t sitp_protocol;
  uint16_t sitp_local_board_id;
  uint16_t sitp_remote_board_id;
  uint16_t sitp_local_port;
  uint16_t sitp_remote_port;
  long sitp_send_delay_ns;

  int log_level;
  int log_to_file;
  char *log_filename;

  int padding_size;
  int verbose;

} bridge_config_t;

int bridge_config_load(const char *config_file, bridge_config_t *config);
void bridge_config_free(bridge_config_t *config);

#endif
